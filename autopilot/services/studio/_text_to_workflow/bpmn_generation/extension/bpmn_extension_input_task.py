import asyncio
import json

from services.studio._text_to_workflow.bpmn_generation import fps_client
from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnExtensionInputToolResult,
    BpmnRequestContext,
    Connector,
    ElementExtensionItem,
    ExtensionInputRequest,
    ExtensionType,
    SolutionResource,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()


class BpmnExtensionInputTask(BpmnBaseTask):
    def __init__(self):
        super().__init__("extension/extension_input.yaml")

    async def generate(self, context: BpmnRequestContext) -> ToolResult:
        # NOT IN USE YET. For chat.
        output = await self._internal_generate(context.user_request, context.current_bpmn, context.extension_input_schema)

        return BpmnExtensionInputToolResult(
            tool=Tool.EXTENSION,
            explanation=output["explanation"],
            inputJson=output["inputJson"],
        )

    async def _internal_generate(self, user_request: str, current_bpmn: str, input_schema: str | None) -> dict:
        if not input_schema:
            return {"explanation": "No input schema provided", "inputJson": ""}

        system_prompt = self._system_template(self.config["prompt"]["system"]).format(
            input_schema=self.config["prompt"]["input_schema"],
            output_schema=self.config["prompt"]["output_schema"],
            examples=self.config["prompt"]["examples"],
        )

        user_prompt = self._human_template(self.config["prompt"]["user"]).format(
            userRequest=user_request,
            currentBpmn=current_bpmn,
            inputSchema=input_schema,
        )

        result, _ = await self._call_llm(system_prompt, user_prompt, "extension_input_generation_bpmn_model")
        # LOGGER.info(f"Result of extension input is : [{result}]")
        str_result = result.strip("```json\n").strip("\n```")
        return json.loads(str_result)

    async def extension_input_generate(self, context: BpmnRequestContext, request: ExtensionInputRequest) -> None:
        """
        Generate the input for the extension based on the request, assign value to the inputJson.
        """

        ext_type = request.extension_type
        element_ext_items = request.items

        if ext_type not in [
            ExtensionType.RPA_WORKFLOW,
            ExtensionType.AGENT,
            ExtensionType.API_WORKFLOW,
            ExtensionType.AGENTIC_PROCESS,
            ExtensionType.QUEUE,
            ExtensionType.ACTION,
            ExtensionType.CONNECTOR,
            ExtensionType.BUSINESS_RULE,
        ]:
            raise NotImplementedError(f"Support for extension type: {ext_type} is not implemented yet.")

        if ext_type == ExtensionType.CONNECTOR:
            await self._handle_connector_type(context, element_ext_items)
        elif ext_type == ExtensionType.BUSINESS_RULE:
            raise NotImplementedError("Business rule extension input generation is not implemented yet.")
        else:
            await self._handle_solution_resource_type(context, element_ext_items, ext_type)

    async def _handle_connector_type(self, context: BpmnRequestContext, element_ext_items: list[ElementExtensionItem]) -> None:
        """Handle connector type extension input generation."""
        for element_item in element_ext_items:
            resources = element_item.suggestions

            if all(isinstance(item, Connector) for item in resources):
                # connector activity
                # if item.activity:
                tasks = [self._get_connector_inputs(context, connector) for connector in resources if isinstance(connector, Connector)]
                results = await asyncio.gather(*tasks)

                # connector trigger
                # if item.trigger:
                # /connections_/api/v1/Connections/{connectionId}/operations?allFolders=true
                # filter out base on event and opration name and find parameter
                # /elements_/v3/element/instances/{instanceId}/elements/{connectorKey}/events/operations/{eventOperation}/objects/{object}/metadata?{parameterName}=undefined&allFields=true

                for resource_item, res in zip(resources, results, strict=False):
                    resource_item.inputJson = res["inputJson"]
            else:
                raise ValueError("Expected all items to be Connector type for connector extension, but it is not, please check extension selection task.")

    async def _handle_solution_resource_type(self, context: BpmnRequestContext, element_ext_items: list[ElementExtensionItem], ext_type: ExtensionType) -> None:
        """Handle solution resource type extension input generation (for other types)."""
        for element_item in element_ext_items:
            resources = element_item.suggestions

            if all(isinstance(resource, SolutionResource) for resource in resources):
                tasks = [self._get_solution_resource_inputs(context, resource, ext_type) for resource in resources if isinstance(resource, SolutionResource)]
                results = await asyncio.gather(*tasks)

                for resource_item, res in zip(resources, results, strict=False):
                    resource_item.inputJson = res["inputJson"]
            else:
                raise ValueError(
                    "Expected all items to be SolutionResource type for this extension type, but it is not, please check extension selection task."
                )

    async def _get_connector_inputs(
        self,
        context: BpmnRequestContext,
        connector: Connector,
    ) -> dict[str, str]:
        connector_metadata = await fps_client.get_connector_metadata(context.request_context, connector)
        if not connector_metadata:
            LOGGER.warning(f"Failed to get metadata for connector {connector.key}")
            return {"explanation": "Failed to get metadata for connector", "inputJson": ""}

        res = await self._internal_generate(context.user_request, context.current_bpmn, connector_metadata)
        return res

    async def _get_solution_resource_inputs(self, context: BpmnRequestContext, item: SolutionResource, extension_type: ExtensionType) -> dict[str, str]:
        reference_key = await fps_client.get_extension_reference_key(context.request_context, context.solution_id, item)
        if not reference_key:
            LOGGER.warning(f"Failed to get reference key for solution resource {item.id}")
            return {"explanation": "Failed to get reference key for solution resource", "inputJson": ""}
        input_schema_escaped = await fps_client.get_extension_configuration_input_schema_raw(
            context.request_context, context.solution_id, reference_key, extension_type
        )

        if not input_schema_escaped:
            LOGGER.warning(f"No input schema for solution resource {item.id}, skip to return empty input json")
            return {"explanation": "No input schema for solution resource {item.id}, skip to return empty input json", "inputJson": ""}

        try:
            unescaped = input_schema_escaped.encode().decode("unicode_escape")
            parsed_json = json.loads(unescaped)
            pretty_json = json.dumps(parsed_json)
        except json.JSONDecodeError as e:
            LOGGER.warning(f"Failed to parse input schema for solution resource {item.id}: {e}")
            return {"explanation": "Failed to parse input schema for solution resource", "inputJson": ""}

        res = await self._internal_generate(context.user_request, context.current_bpmn, pretty_json)
        return res
